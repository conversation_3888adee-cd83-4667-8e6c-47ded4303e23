"""
完整的向量数据库实现
支持向量存储、相似度检索、持久化、索引优化等功能
适用于新闻内容生成系统的后续开发
"""

import numpy as np
import json
import os
import pickle
import sqlite3
import time
from typing import List, Dict, Tuple, Optional, Union
from datetime import datetime
import threading
from collections import defaultdict


class VectorDatabase:
    """
    完整的向量数据库实现
    
    功能特性：
    - 高效的向量存储和检索
    - 支持元数据过滤
    - 持久化存储
    - 索引优化
    - 批量操作
    - 线程安全
    """
    
    def __init__(self, db_path: str = "vector_db", vector_dim: int = 768):
        """
        初始化向量数据库
        
        Args:
            db_path: 数据库存储路径
            vector_dim: 向量维度
        """
        self.db_path = db_path
        self.vector_dim = vector_dim
        
        # 内存存储
        self.vectors = {}  # {doc_id: numpy.array}
        self.metadata = {}  # {doc_id: dict}
        self.doc_ids = []  # 文档ID列表
        
        # 索引
        self.category_index = defaultdict(list)  # 分类索引
        self.source_index = defaultdict(list)    # 来源索引
        self.time_index = defaultdict(list)      # 时间索引
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_documents': 0,
            'total_searches': 0,
            'last_updated': None,
            'created_at': datetime.now().isoformat()
        }
        
        # 创建存储目录
        os.makedirs(db_path, exist_ok=True)
        
        # 初始化SQLite数据库（用于元数据）
        self._init_sqlite_db()
        
        # 尝试加载已有数据
        self.load_database()
        
        print(f"向量数据库初始化完成")
        print(f"   存储路径: {self.db_path}")
        print(f"   向量维度: {self.vector_dim}")
        print(f"   已有文档: {len(self.doc_ids)}")
    
    def _init_sqlite_db(self):
        """初始化SQLite数据库"""
        db_file = os.path.join(self.db_path, "metadata.db")
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 创建元数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    doc_id TEXT PRIMARY KEY,
                    title TEXT,
                    content TEXT,
                    category TEXT,
                    source TEXT,
                    publish_time TEXT,
                    quality_score REAL,
                    created_at TEXT,
                    updated_at TEXT,
                    metadata_json TEXT
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_category ON documents(category)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_source ON documents(source)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_publish_time ON documents(publish_time)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_quality_score ON documents(quality_score)")
            
            # 创建统计表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stats (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TEXT
                )
            """)
            
            conn.commit()
    
    def add_document(self, doc_id: str, vector: Union[List[float], np.ndarray], metadata: Dict) -> bool:
        """
        添加文档到向量数据库
        
        Args:
            doc_id: 文档唯一ID
            vector: 文档向量
            metadata: 文档元数据
            
        Returns:
            是否添加成功
        """
        with self.lock:
            try:
                # 转换向量格式
                if isinstance(vector, list):
                    vector = np.array(vector, dtype=np.float32)
                elif isinstance(vector, np.ndarray):
                    vector = vector.astype(np.float32)
                else:
                    raise ValueError("向量必须是list或numpy.ndarray类型")
                
                # 检查向量维度
                if len(vector) != self.vector_dim:
                    raise ValueError(f"向量维度不匹配，期望{self.vector_dim}，实际{len(vector)}")
                
                # 归一化向量（用于余弦相似度计算）
                norm = np.linalg.norm(vector)
                if norm > 0:
                    vector = vector / norm
                
                # 检查是否已存在
                if doc_id in self.vectors:
                    print(f"文档 {doc_id} 已存在，将更新")
                    self._remove_from_indexes(doc_id)
                else:
                    self.doc_ids.append(doc_id)
                
                # 存储向量和元数据
                self.vectors[doc_id] = vector
                self.metadata[doc_id] = metadata.copy()
                
                # 更新索引
                self._update_indexes(doc_id, metadata)
                
                # 保存到SQLite
                self._save_to_sqlite(doc_id, metadata)
                
                # 更新统计
                self.stats['total_documents'] = len(self.doc_ids)
                self.stats['last_updated'] = datetime.now().isoformat()
                
                return True
                
            except Exception as e:
                print(f"添加文档失败: {e}")
                return False
    
    def _update_indexes(self, doc_id: str, metadata: Dict):
        """更新索引"""
        # 分类索引
        category = metadata.get('category', 'unknown')
        self.category_index[category].append(doc_id)
        
        # 来源索引
        source = metadata.get('source', 'unknown')
        self.source_index[source].append(doc_id)
        
        # 时间索引（按日期分组）
        publish_time = metadata.get('publish_time', '')
        if publish_time:
            date_key = publish_time[:10]  # YYYY-MM-DD
            self.time_index[date_key].append(doc_id)
    
    def _remove_from_indexes(self, doc_id: str):
        """从索引中移除文档"""
        if doc_id not in self.metadata:
            return
        
        metadata = self.metadata[doc_id]
        
        # 从分类索引移除
        category = metadata.get('category', 'unknown')
        if doc_id in self.category_index[category]:
            self.category_index[category].remove(doc_id)
        
        # 从来源索引移除
        source = metadata.get('source', 'unknown')
        if doc_id in self.source_index[source]:
            self.source_index[source].remove(doc_id)
        
        # 从时间索引移除
        publish_time = metadata.get('publish_time', '')
        if publish_time:
            date_key = publish_time[:10]
            if doc_id in self.time_index[date_key]:
                self.time_index[date_key].remove(doc_id)
    
    def _save_to_sqlite(self, doc_id: str, metadata: Dict):
        """保存元数据到SQLite"""
        db_file = os.path.join(self.db_path, "metadata.db")
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO documents 
                (doc_id, title, content, category, source, publish_time, 
                 quality_score, created_at, updated_at, metadata_json)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                doc_id,
                metadata.get('title', ''),
                metadata.get('content', ''),
                metadata.get('category', ''),
                metadata.get('source', ''),
                metadata.get('publish_time', ''),
                metadata.get('quality_score', 0.0),
                metadata.get('created_at', datetime.now().isoformat()),
                datetime.now().isoformat(),
                json.dumps(metadata, ensure_ascii=False)
            ))
            
            conn.commit()
    
    def search(self, query_vector: Union[List[float], np.ndarray], 
               top_k: int = 10, threshold: float = 0.0,
               filters: Optional[Dict] = None) -> List[Dict]:
        """
        向量相似度搜索
        
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的K个结果
            threshold: 相似度阈值
            filters: 过滤条件 {'category': 'news', 'source': 'sina'}
            
        Returns:
            相似文档列表
        """
        with self.lock:
            self.stats['total_searches'] += 1
            
            if not self.vectors:
                return []
            
            try:
                # 转换查询向量
                if isinstance(query_vector, list):
                    query_vector = np.array(query_vector, dtype=np.float32)
                
                # 归一化查询向量
                norm = np.linalg.norm(query_vector)
                if norm > 0:
                    query_vector = query_vector / norm
                
                # 获取候选文档ID
                candidate_ids = self._get_filtered_candidates(filters)
                
                if not candidate_ids:
                    return []
                
                # 计算相似度
                similarities = []
                for doc_id in candidate_ids:
                    if doc_id in self.vectors:
                        similarity = np.dot(query_vector, self.vectors[doc_id])
                        if similarity >= threshold:
                            similarities.append((doc_id, float(similarity)))
                
                # 按相似度排序
                similarities.sort(key=lambda x: x[1], reverse=True)
                
                # 构建结果
                results = []
                for i, (doc_id, similarity) in enumerate(similarities[:top_k]):
                    result = {
                        'doc_id': doc_id,
                        'similarity': similarity,
                        'rank': i + 1,
                        'metadata': self.metadata.get(doc_id, {})
                    }
                    results.append(result)
                
                return results
                
            except Exception as e:
                print(f"搜索失败: {e}")
                return []
    
    def _get_filtered_candidates(self, filters: Optional[Dict]) -> List[str]:
        """根据过滤条件获取候选文档"""
        if not filters:
            return self.doc_ids.copy()
        
        candidate_sets = []
        
        # 分类过滤
        if 'category' in filters:
            category = filters['category']
            candidate_sets.append(set(self.category_index.get(category, [])))
        
        # 来源过滤
        if 'source' in filters:
            source = filters['source']
            candidate_sets.append(set(self.source_index.get(source, [])))
        
        # 时间过滤
        if 'date' in filters:
            date = filters['date']
            candidate_sets.append(set(self.time_index.get(date, [])))
        
        # 质量分数过滤
        if 'min_quality' in filters:
            min_quality = filters['min_quality']
            quality_candidates = [
                doc_id for doc_id in self.doc_ids
                if self.metadata.get(doc_id, {}).get('quality_score', 0) >= min_quality
            ]
            candidate_sets.append(set(quality_candidates))
        
        # 取交集
        if candidate_sets:
            result_set = candidate_sets[0]
            for candidate_set in candidate_sets[1:]:
                result_set = result_set.intersection(candidate_set)
            return list(result_set)
        
        return self.doc_ids.copy()
    
    def batch_add_documents(self, documents: List[Tuple[str, Union[List[float], np.ndarray], Dict]]) -> Dict:
        """
        批量添加文档
        
        Args:
            documents: [(doc_id, vector, metadata), ...]
            
        Returns:
            批量操作结果统计
        """
        print(f"开始批量添加 {len(documents)} 个文档...")

        results = {
            'total': len(documents),
            'success': 0,
            'failed': 0,
            'errors': []
        }

        for i, (doc_id, vector, metadata) in enumerate(documents, 1):
            try:
                if self.add_document(doc_id, vector, metadata):
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append(f"文档 {doc_id} 添加失败")

                # 进度显示
                if i % 100 == 0:
                    print(f"   进度: {i}/{len(documents)} ({i/len(documents)*100:.1f}%)")

            except Exception as e:
                results['failed'] += 1
                results['errors'].append(f"文档 {doc_id} 异常: {str(e)}")

        print(f"批量添加完成: {results['success']} 成功, {results['failed']} 失败")
        return results
    
    def get_document(self, doc_id: str) -> Optional[Dict]:
        """获取文档信息"""
        with self.lock:
            if doc_id not in self.metadata:
                return None
            
            return {
                'doc_id': doc_id,
                'vector': self.vectors.get(doc_id),
                'metadata': self.metadata.get(doc_id, {})
            }
    
    def delete_document(self, doc_id: str) -> bool:
        """删除文档"""
        with self.lock:
            if doc_id not in self.vectors:
                return False
            
            try:
                # 从内存中删除
                del self.vectors[doc_id]
                del self.metadata[doc_id]
                self.doc_ids.remove(doc_id)
                
                # 从索引中删除
                self._remove_from_indexes(doc_id)
                
                # 从SQLite中删除
                db_file = os.path.join(self.db_path, "metadata.db")
                with sqlite3.connect(db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM documents WHERE doc_id = ?", (doc_id,))
                    conn.commit()
                
                # 更新统计
                self.stats['total_documents'] = len(self.doc_ids)
                self.stats['last_updated'] = datetime.now().isoformat()
                
                return True
                
            except Exception as e:
                print(f"删除文档失败: {e}")
                return False
    
    def save_database(self):
        """保存数据库到文件"""
        with self.lock:
            try:
                print(f"保存向量数据库...")

                # 保存向量数据
                vectors_file = os.path.join(self.db_path, "vectors.pkl")
                with open(vectors_file, 'wb') as f:
                    pickle.dump({
                        'vectors': self.vectors,
                        'doc_ids': self.doc_ids,
                        'vector_dim': self.vector_dim
                    }, f)

                # 保存索引
                indexes_file = os.path.join(self.db_path, "indexes.pkl")
                with open(indexes_file, 'wb') as f:
                    pickle.dump({
                        'category_index': dict(self.category_index),
                        'source_index': dict(self.source_index),
                        'time_index': dict(self.time_index)
                    }, f)

                # 保存统计信息
                stats_file = os.path.join(self.db_path, "stats.json")
                with open(stats_file, 'w', encoding='utf-8') as f:
                    json.dump(self.stats, f, ensure_ascii=False, indent=2)

                print(f"数据库已保存到 {self.db_path}")
                print(f"   文档数量: {len(self.doc_ids)}")
                print(f"   向量维度: {self.vector_dim}")

            except Exception as e:
                print(f"保存数据库失败: {e}")
    
    def load_database(self):
        """从文件加载数据库"""
        try:
            vectors_file = os.path.join(self.db_path, "vectors.pkl")
            indexes_file = os.path.join(self.db_path, "indexes.pkl")
            stats_file = os.path.join(self.db_path, "stats.json")
            
            # 加载向量数据
            if os.path.exists(vectors_file):
                with open(vectors_file, 'rb') as f:
                    data = pickle.load(f)
                    self.vectors = data.get('vectors', {})
                    self.doc_ids = data.get('doc_ids', [])
                    saved_dim = data.get('vector_dim', self.vector_dim)
                    
                    if saved_dim != self.vector_dim:
                        print(f"⚠️ 向量维度不匹配: 保存的{saved_dim} vs 当前{self.vector_dim}")
            
            # 加载索引
            if os.path.exists(indexes_file):
                with open(indexes_file, 'rb') as f:
                    data = pickle.load(f)
                    self.category_index = defaultdict(list, data.get('category_index', {}))
                    self.source_index = defaultdict(list, data.get('source_index', {}))
                    self.time_index = defaultdict(list, data.get('time_index', {}))
            
            # 加载统计信息
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    self.stats.update(json.load(f))
            
            # 从SQLite加载元数据
            self._load_metadata_from_sqlite()
            
            if self.doc_ids:
                print(f"从 {self.db_path} 加载了 {len(self.doc_ids)} 个文档")

        except Exception as e:
            print(f"加载数据库时出错: {e}")
            print("将创建新的数据库")
    
    def _load_metadata_from_sqlite(self):
        """从SQLite加载元数据"""
        db_file = os.path.join(self.db_path, "metadata.db")
        
        if not os.path.exists(db_file):
            return
        
        try:
            with sqlite3.connect(db_file) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT doc_id, metadata_json FROM documents")
                
                for doc_id, metadata_json in cursor.fetchall():
                    if doc_id in self.vectors:  # 只加载有向量的文档的元数据
                        try:
                            self.metadata[doc_id] = json.loads(metadata_json)
                        except json.JSONDecodeError:
                            print(f"文档 {doc_id} 的元数据解析失败")

        except Exception as e:
            print(f"从SQLite加载元数据失败: {e}")
    
    def get_stats(self) -> Dict:
        """获取数据库统计信息"""
        with self.lock:
            # 实时统计
            categories = defaultdict(int)
            sources = defaultdict(int)
            quality_scores = []
            
            for metadata in self.metadata.values():
                categories[metadata.get('category', 'unknown')] += 1
                sources[metadata.get('media', 'unknown')] += 1  # 修复：使用'media'字段而不是'source'
                quality_scores.append(metadata.get('quality_score', 0.0))
            
            stats = self.stats.copy()
            stats.update({
                'total_documents': len(self.doc_ids),
                'vector_dimension': self.vector_dim,
                'storage_path': self.db_path,
                'categories': dict(categories),
                'sources': dict(sources),
                'avg_quality_score': np.mean(quality_scores) if quality_scores else 0.0,
                'memory_usage_mb': self._estimate_memory_usage()
            })
            
            return stats
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        try:
            vector_size = len(self.vectors) * self.vector_dim * 4  # float32
            metadata_size = sum(len(str(meta)) for meta in self.metadata.values())
            return (vector_size + metadata_size) / (1024 * 1024)
        except:
            return 0.0
    
    def print_stats(self):
        """打印数据库统计信息"""
        stats = self.get_stats()

        print("\n向量数据库统计信息:")
        print(f"   总文档数: {stats['total_documents']}")
        print(f"   向量维度: {stats['vector_dimension']}")
        print(f"   存储路径: {stats['storage_path']}")
        print(f"   总搜索次数: {stats['total_searches']}")
        print(f"   平均质量分: {stats['avg_quality_score']:.2f}")
        print(f"   内存使用: {stats['memory_usage_mb']:.1f} MB")

        if stats['categories']:
            print(f"   分类分布: {dict(list(stats['categories'].items())[:5])}")

        if stats['sources']:
            print(f"   来源分布: {dict(list(stats['sources'].items())[:5])}")

        print(f"   创建时间: {stats['created_at']}")
        print(f"   最后更新: {stats['last_updated']}")

    def optimize_database(self):
        """优化数据库性能"""
        with self.lock:
            try:
                print("🔧 开始数据库优化...")

                # 1. 清理空的索引条目
                self._cleanup_empty_indexes()

                # 2. 重建索引
                self._rebuild_indexes()

                # 3. 优化SQLite数据库
                self._optimize_sqlite()

                # 4. 保存优化后的数据
                self.save_database()

                print("✅ 数据库优化完成")

            except Exception as e:
                print(f"❌ 数据库优化失败: {e}")

    def _cleanup_empty_indexes(self):
        """清理空的索引条目"""
        # 清理分类索引
        empty_categories = [cat for cat, docs in self.category_index.items() if not docs]
        for cat in empty_categories:
            del self.category_index[cat]

        # 清理来源索引
        empty_sources = [src for src, docs in self.source_index.items() if not docs]
        for src in empty_sources:
            del self.source_index[src]

        # 清理时间索引
        empty_dates = [date for date, docs in self.time_index.items() if not docs]
        for date in empty_dates:
            del self.time_index[date]

    def _rebuild_indexes(self):
        """重建索引"""
        # 清空现有索引
        self.category_index.clear()
        self.source_index.clear()
        self.time_index.clear()

        # 重新构建索引
        for doc_id in self.doc_ids:
            if doc_id in self.metadata:
                self._update_indexes(doc_id, self.metadata[doc_id])

    def _optimize_sqlite(self):
        """优化SQLite数据库"""
        db_file = os.path.join(self.db_path, "metadata.db")

        if os.path.exists(db_file):
            with sqlite3.connect(db_file) as conn:
                cursor = conn.cursor()

                # 执行VACUUM命令优化数据库
                cursor.execute("VACUUM")

                # 分析表以优化查询计划
                cursor.execute("ANALYZE")

                conn.commit()


# 便捷别名，保持向后兼容
SimpleVectorDB = VectorDatabase


if __name__ == "__main__":
    # 测试代码
    print("向量数据库测试")

    # 创建数据库
    db = VectorDatabase("test_db", vector_dim=768)

    # 添加测试文档
    test_vector = np.random.rand(768).tolist()
    test_metadata = {
        'title': '测试新闻',
        'content': '这是一条测试新闻内容',
        'category': 'test',
        'source': 'test_source',
        'quality_score': 0.8
    }

    success = db.add_document("test_001", test_vector, test_metadata)
    print(f"添加文档: {'成功' if success else '失败'}")

    # 搜索测试
    results = db.search(test_vector, top_k=5)
    print(f"搜索结果: {len(results)} 条")

    # 显示统计
    db.print_stats()

    # 保存数据库
    db.save_database()

    print("测试完成")
