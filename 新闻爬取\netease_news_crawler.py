#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网易新闻要闻爬虫
爬取网易新闻首页的要闻部分内容
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import csv
from datetime import datetime
import re

class NeteaseNewsCrawler:
    def __init__(self, max_news=20, get_detail=True, get_all=False):
        """
        初始化爬虫
        :param max_news: 最大爬取新闻数量，默认20条，如果get_all=True则忽略此参数
        :param get_detail: 是否获取详细内容，默认True
        :param get_all: 是否获取所有新闻，默认False
        """
        self.base_url = "https://news.163.com/"
        self.max_news = max_news
        self.get_detail = get_detail
        self.get_all = get_all
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def is_today_article(self, create_date: str, create_time: str = None, time_info: str = None) -> bool:
        """判断文章是否为今天发布"""
        try:
            # 获取今天的日期
            today = datetime.now().strftime('%Y-%m-%d')

            # 如果有原始时间信息，优先使用原始信息判断
            if time_info:
                # 检查是否包含"今天"、"分钟前"、"小时前"等今日标识
                today_patterns = [
                    r'今天\s*\d{1,2}:\d{2}',
                    r'\d+分钟前',
                    r'\d+小时前',
                    r'刚刚'
                ]

                for pattern in today_patterns:
                    if re.search(pattern, time_info):
                        return True

                # 检查是否包含"昨天"等非今日标识
                if re.search(r'昨天|前天|\d+天前', time_info):
                    return False

                # 检查今天的日期格式
                today_date = datetime.now()
                date_patterns = [
                    f"{today_date.month:02d}-{today_date.day:02d}",
                    f"{today_date.month}/{today_date.day}",
                    today.split('-')[1] + '-' + today.split('-')[2]  # MM-DD格式
                ]

                for pattern in date_patterns:
                    if pattern in time_info:
                        return True

            # 如果有明确的create_date，使用它进行判断
            if create_date:
                # 如果create_date格式为YYYY-MM-DD，直接比较
                if len(create_date) == 10 and '-' in create_date:
                    return create_date == today

                # 如果create_date格式为YYYYMMDD，转换后比较
                if len(create_date) == 8 and create_date.isdigit():
                    formatted_date = f"{create_date[:4]}-{create_date[4:6]}-{create_date[6:8]}"
                    return formatted_date == today

                # 如果create_date格式为MM-DD，补充年份后比较
                if len(create_date) == 5 and '-' in create_date:
                    current_year = datetime.now().year
                    full_date = f"{current_year}-{create_date}"
                    return full_date == today

            # 对于网易新闻首页，如果没有明确的时间信息，
            # 假设首页的新闻都是相对较新的，允许通过
            # 这是因为网易首页通常不显示具体的发布时间
            if not time_info and not create_date:
                return True

            return False

        except Exception as e:
            print(f"日期判断出错: {e}")
            return False

    def get_page_content(self):
        """获取网页内容"""
        try:
            print("正在获取网易新闻首页...")
            response = self.session.get(self.base_url, timeout=10)
            response.encoding = 'utf-8'
            return response.text
        except Exception as e:
            print(f"获取网页内容失败: {e}")
            return None
    
    def parse_news_items(self, html_content):
        """解析新闻条目"""
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []
        
        print("正在解析要闻内容...")
        
        # 方法1: 查找主要新闻区域的链接
        # 网易新闻的要闻通常在主要内容区域
        main_content_selectors = [
            'div.index2016_content',  # 主要内容区域
            'div.festival_main',      # 节日主要区域
            'div[class*="news"]',     # 包含news的div
        ]
        
        for selector in main_content_selectors:
            try:
                containers = soup.select(selector)
                for container in containers:
                    # 查找新闻链接
                    links = container.find_all('a', href=True)
                    for link in links:
                        href = link.get('href')
                        title = link.get_text(strip=True)
                        
                        # 过滤有效的新闻链接
                        if self.is_valid_news_link(href, title):
                            news_id = len(news_items) + 1  # 生成递增的ID
                            news_item = self.extract_news_info(link, href, title, news_id)
                            if news_item and news_item not in news_items:
                                news_items.append(news_item)
                
                # 如果找到了新闻，就不再查找其他选择器
                if news_items:
                    break
                    
            except Exception as e:
                print(f"解析选择器 {selector} 时出错: {e}")
                continue
        
        # 方法2: 如果上面没找到，尝试直接查找所有新闻链接
        if not news_items:
            print("使用备用方法查找新闻...")
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link.get('href')
                title = link.get_text(strip=True)
                
                if self.is_valid_news_link(href, title):
                    news_id = len(news_items) + 1  # 生成递增的ID
                    news_item = self.extract_news_info(link, href, title, news_id)
                    if news_item and news_item not in news_items:
                        news_items.append(news_item)

                # 限制数量，避免获取太多
                if len(news_items) >= 50:
                    break
        
        # 去重并排序
        unique_news = []
        seen_titles = set()
        for item in news_items:
            if item['title'] not in seen_titles:
                unique_news.append(item)
                seen_titles.add(item['title'])

        print(f"共解析到 {len(unique_news)} 条要闻")

        # 过滤当天新闻
        today_news = []
        skipped_count = 0
        no_time_count = 0

        for item in unique_news:
            time_info = item.get('time_info', '')
            create_date = item.get('create_date', '')

            if not time_info and not create_date:
                no_time_count += 1

            is_today = self.is_today_article(create_date, item.get('create_time', ''), time_info)

            if is_today:
                today_news.append(item)
            else:
                skipped_count += 1
                # 显示跳过的新闻信息用于调试
                if skipped_count <= 3:  # 只显示前3条跳过的新闻
                    reason = f"时间: {time_info or '无'}"
                    if create_date:
                        reason += f", 日期: {create_date}"
                    print(f"⚠️ 跳过非当天新闻: {item['title'][:40]}... ({reason})")

        print(f"网易新闻统计: 总计 {len(unique_news)} 条")
        print(f"  ✅ 当天新闻: {len(today_news)} 条")
        print(f"  ⚠️ 跳过新闻: {skipped_count} 条")
        print(f"  📝 无时间信息: {no_time_count} 条 (已按首页新闻处理)")

        # 根据设置限制数量
        if self.get_all:
            final_news = today_news
            print(f"将获取全部 {len(final_news)} 条当天新闻")
        else:
            final_news = today_news[:self.max_news]
            print(f"将获取前 {len(final_news)} 条当天新闻")

        # 如果需要获取详细内容
        if self.get_detail:
            print(f"开始获取详细内容...")
            filtered_news = []
            for i, item in enumerate(final_news):
                print(f"正在获取第 {i+1}/{len(final_news)} 条新闻的详细内容...")
                detail = self.get_article_detail(item['url'])

                # 更新新闻项目
                item.update(detail)

                # 如果获取到了真实的发布时间，用它来重新判断是否为当天新闻
                if detail.get('real_create_date'):
                    if self.is_today_article(detail['real_create_date'], detail.get('real_create_time', ''), detail.get('real_publish_time', '')):
                        # 更新新闻的时间信息为真实时间
                        item['create_date'] = detail['real_create_date']
                        item['create_time'] = detail['real_create_time']
                        item['formatted_time'] = detail['real_publish_time']
                        filtered_news.append(item)
                    else:
                        print(f"  ⚠️ 跳过非当天新闻: {item['title'][:40]}... (真实时间: {detail['real_publish_time']})")
                else:
                    # 没有获取到真实时间，保留原来的判断
                    filtered_news.append(item)

                # 添加延时避免请求过快
                if i < len(final_news) - 1:  # 最后一个不需要延时
                    time.sleep(0.5)  # 减少延时时间

            final_news = filtered_news
            print(f"获取详情后，剩余当天新闻: {len(final_news)} 条")
        else:
            print("跳过获取详细内容")

        return final_news
    
    def is_valid_news_link(self, href, title):
        """判断是否为有效的新闻链接"""
        if not href or not title:
            return False
        
        # 过滤掉太短的标题
        if len(title) < 5:
            return False
        
        # 过滤掉导航链接
        nav_keywords = ['首页', '国内', '国际', '体育', '娱乐', '财经', '科技', '汽车', '房产', '教育', '健康', '旅游']
        if title in nav_keywords:
            return False
        
        # 只保留新闻文章链接
        valid_patterns = [
            r'news\.163\.com/.*\.html',
            r'www\.163\.com/news/article/',
            r'www\.163\.com/dy/article/',
        ]
        
        for pattern in valid_patterns:
            if re.search(pattern, href):
                return True
        
        return False
    
    def extract_news_info(self, link_element, href, title, news_id):
        """提取新闻信息"""
        try:
            # 确保链接是完整的URL
            if href.startswith('//'):
                href = 'https:' + href
            elif href.startswith('/'):
                href = 'https://www.163.com' + href
            elif not href.startswith('http'):
                href = 'https://www.163.com/' + href

            # 查找可能的时间信息
            time_info = ""
            parent = link_element.parent
            if parent:
                # 扩大搜索范围，查找更多可能包含时间的元素
                time_elements = parent.find_all(['span', 'time', 'div', 'p'],
                                              class_=re.compile(r'time|date|publish|update'))
                for time_elem in time_elements:
                    time_text = time_elem.get_text(strip=True)
                    if re.search(r'\d{2}:\d{2}|\d{4}-\d{2}-\d{2}|\d{2}-\d{2}|今天|昨天|分钟前|小时前', time_text):
                        time_info = time_text
                        break

                # 如果还没找到，尝试在父元素的所有文本中查找
                if not time_info:
                    all_text = parent.get_text()
                    time_patterns = [
                        r'今天\s*\d{1,2}:\d{2}',
                        r'昨天\s*\d{1,2}:\d{2}',
                        r'\d+分钟前',
                        r'\d+小时前',
                        r'\d{4}-\d{2}-\d{2}\s*\d{2}:\d{2}',
                        r'\d{2}-\d{2}\s*\d{2}:\d{2}'
                    ]
                    for pattern in time_patterns:
                        match = re.search(pattern, all_text)
                        if match:
                            time_info = match.group()
                            break

            # 当前时间
            now = datetime.now()
            crawl_time = now.strftime('%Y-%m-%d %H:%M:%S')

            # 格式化时间（如果有的话）
            formatted_time = ""
            create_date = ""
            create_time = ""

            if time_info:
                # 尝试解析时间
                try:
                    if re.match(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', time_info):
                        dt = datetime.strptime(time_info, '%Y-%m-%d %H:%M:%S')
                        create_date = dt.strftime('%Y-%m-%d')
                        create_time = dt.strftime('%H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    elif re.match(r'\d{4}-\d{2}-\d{2}', time_info):
                        dt = datetime.strptime(time_info, '%Y-%m-%d')
                        create_date = dt.strftime('%Y-%m-%d')
                        create_time = "00:00:00"
                        formatted_time = dt.strftime('%m-%d')
                    else:
                        # 无法解析具体日期，保留原始时间信息用于判断
                        create_date = ""  # 不设置具体日期
                        create_time = ""
                        formatted_time = time_info  # 保留原始时间文本
                except:
                    # 如果解析失败，保留原始时间信息
                    create_date = ""
                    create_time = ""
                    formatted_time = time_info
            else:
                # 没有时间信息，无法确定日期
                create_date = ""
                create_time = ""
                formatted_time = ""

            return {
                'id': news_id,
                'title': title,
                'url': href,
                'media': '网易新闻',  # 默认媒体来源
                'create_date': create_date,
                'create_time': create_time,
                'formatted_time': formatted_time,
                'time_info': time_info,  # 保存原始时间信息用于判断
                'category': '要闻',  # 默认分类
                'ranking_type': '要闻排行',
                'rank': news_id,  # 使用ID作为排名
                'comment_url': '',  # 评论链接，暂时为空
                'top_num': '',  # 热度数字，暂时为空
                'crawl_time': crawl_time,
                'content': ""  # 稍后填充
            }
        except Exception as e:
            print(f"提取新闻信息时出错: {e}")
            return None

    def get_article_detail(self, url):
        """获取文章详细内容"""
        try:
            print(f"正在获取文章详情: {url}")
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            # 首先提取真实的发布时间
            real_publish_time = ""
            real_create_date = ""
            real_create_time = ""

            # 从 post_info 元素中提取发布时间
            post_info = soup.select_one('div.post_info')
            if post_info:
                info_text = post_info.get_text(strip=True)
                # 匹配时间格式：2025-07-04 20:56:52
                time_match = re.search(r'(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})', info_text)
                if time_match:
                    real_create_date = time_match.group(1)
                    real_create_time = time_match.group(2)
                    real_publish_time = f"{real_create_date} {real_create_time}"
                    print(f"  📅 提取到发布时间: {real_publish_time}")

            # 提取文章内容
            content = ""
            content_selectors = [
                'div.post_content_main',  # 网易新闻主要内容区域
                'div.post_text',          # 文章文本
                'div.content',            # 通用内容区域
                'div[class*="content"]',  # 包含content的div
                'article',                # 文章标签
                'div.article-body',       # 文章主体
            ]

            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    # 移除广告和无关元素
                    for ad in content_elem.find_all(['script', 'style', 'iframe', 'ins']):
                        ad.decompose()

                    # 提取文本内容
                    paragraphs = content_elem.find_all(['p', 'div'])
                    content_parts = []
                    for p in paragraphs:
                        text = p.get_text(strip=True)
                        if text and len(text) > 10:  # 过滤太短的段落
                            content_parts.append(text)

                    content = '\n'.join(content_parts)
                    if content:
                        break

            # 如果没找到内容，尝试获取所有段落
            if not content:
                all_paragraphs = soup.find_all('p')
                content_parts = []
                for p in all_paragraphs:
                    text = p.get_text(strip=True)
                    if text and len(text) > 20:
                        content_parts.append(text)
                content = '\n'.join(content_parts[:10])  # 最多取前10段

            # 提取发布时间
            publish_time = ""
            time_selectors = [
                'span.post_time_source',
                'div.post_time_source',
                'span[class*="time"]',
                'div[class*="time"]',
                'time',
            ]

            for selector in time_selectors:
                time_elem = soup.select_one(selector)
                if time_elem:
                    time_text = time_elem.get_text(strip=True)
                    # 提取时间信息
                    time_match = re.search(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}|\d{4}-\d{2}-\d{2}|\d{2}-\d{2}\s+\d{2}:\d{2}', time_text)
                    if time_match:
                        publish_time = time_match.group()
                        break

            # 提取作者信息
            author = ""
            author_selectors = [
                'span.ep-editor',
                'div.ep-editor',
                'span[class*="author"]',
                'div[class*="author"]',
                'span[class*="editor"]',
            ]

            for selector in author_selectors:
                author_elem = soup.select_one(selector)
                if author_elem:
                    author_text = author_elem.get_text(strip=True)
                    if author_text:
                        author = author_text
                        break

            # 提取来源信息
            source = ""
            source_selectors = [
                'span.ep-source',
                'div.ep-source',
                'span[class*="source"]',
                'div[class*="source"]',
            ]

            for selector in source_selectors:
                source_elem = soup.select_one(selector)
                if source_elem:
                    source_text = source_elem.get_text(strip=True)
                    if source_text:
                        source = source_text
                        break

            # 提取真实的发布时间
            real_publish_time = ""
            real_create_date = ""
            real_create_time = ""

            # 从 post_info 元素中提取发布时间
            post_info = soup.select_one('div.post_info')
            if post_info:
                info_text = post_info.get_text(strip=True)
                # 匹配时间格式：2025-07-04 20:56:52
                time_match = re.search(r'(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})', info_text)
                if time_match:
                    real_create_date = time_match.group(1)
                    real_create_time = time_match.group(2)
                    real_publish_time = f"{real_create_date} {real_create_time}"

            return {
                'content': content[:2000] if content else "",  # 限制长度
                'real_create_date': real_create_date,
                'real_create_time': real_create_time,
                'real_publish_time': real_publish_time,
            }

        except Exception as e:
            print(f"获取文章详情失败 {url}: {e}")
            return {
                'content': "",
            }


    
    def save_to_json(self, news_items, filename='netease_news.json'):
        """保存为JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_items, f, ensure_ascii=False, indent=2)
            print(f"新闻数据已保存到 {filename}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
    
    def save_to_csv(self, news_items, filename='netease_news.csv'):
        """保存为CSV文件"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                # 使用与目标文件相同的字段名
                fieldnames = [
                    'id', 'title', 'url', 'media', 'create_date', 'create_time',
                    'formatted_time', 'category', 'ranking_type', 'rank',
                    'comment_url', 'top_num', 'crawl_time', 'content'
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(news_items)
            print(f"新闻数据已保存到 {filename}")
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
    
    def print_news(self, news_items):
        """打印新闻到控制台"""
        print("\n" + "="*80)
        print("网易新闻 - 要闻")
        print("="*80)

        for i, item in enumerate(news_items, 1):
            print(f"\n{i}. {item['title']}")
            print(f"   链接: {item['url']}")
            if item.get('publish_time'):
                print(f"   发布时间: {item['publish_time']}")
            if item.get('author'):
                print(f"   作者: {item['author']}")
            if item.get('source'):
                print(f"   来源: {item['source']}")
            if item.get('summary'):
                print(f"   摘要: {item['summary']}")
            if item.get('content'):
                content_preview = item['content'][:200] + "..." if len(item['content']) > 200 else item['content']
                print(f"   内容预览: {content_preview}")
            print(f"   爬取时间: {item['crawl_time']}")
            print("-" * 80)
    
    def crawl(self):
        """执行爬取"""
        print("开始爬取网易新闻要闻...")
        
        # 获取网页内容
        html_content = self.get_page_content()
        if not html_content:
            print("无法获取网页内容，爬取失败")
            return []
        
        # 解析新闻
        news_items = self.parse_news_items(html_content)
        
        if news_items:
            # 打印到控制台
            self.print_news(news_items)
            
            # 保存到文件
            self.save_to_json(news_items)
            self.save_to_csv(news_items)
            
            print(f"\n爬取完成！共获取 {len(news_items)} 条要闻")
        else:
            print("未能获取到新闻内容")
        
        return news_items

    def crawl_without_save(self):
        """执行爬取但不保存文件（用于统一爬虫）"""
        # 获取网页内容
        html_content = self.get_page_content()
        if not html_content:
            return []

        # 解析新闻
        news_items = self.parse_news_items(html_content)

        # 如果需要获取详细内容
        if self.get_detail and news_items:
            filtered_news = []
            for i, item in enumerate(news_items):
                if (i + 1) % 20 == 0:  # 每20条显示一次进度
                    print(f"  📰 [线程1-网易] 已处理 {i + 1}/{len(news_items)} 条新闻...")
                detail = self.get_article_detail(item['url'])
                item.update(detail)

                # 如果获取到了真实的发布时间，用它来重新判断是否为当天新闻
                if detail.get('real_create_date'):
                    if self.is_today_article(detail['real_create_date'], detail.get('real_create_time', ''), detail.get('real_publish_time', '')):
                        # 更新新闻的时间信息为真实时间
                        item['create_date'] = detail['real_create_date']
                        item['create_time'] = detail['real_create_time']
                        item['formatted_time'] = detail['real_publish_time']
                        filtered_news.append(item)
                else:
                    # 没有获取到真实时间，保留原来的判断
                    filtered_news.append(item)

                # 添加延时避免请求过快
                if i < len(news_items) - 1:
                    time.sleep(0.5)

            return filtered_news

        return news_items

def main():
    """主函数"""
    import sys

    # 默认参数
    max_news = 20
    get_detail = True
    get_all = False

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['all', 'a', '全部']:
            get_all = True
            max_news = 999  # 设置一个大数值
        else:
            try:
                max_news = int(sys.argv[1])
            except ValueError:
                print("错误：新闻数量必须是数字，或使用 'all' 获取全部新闻")
                return

    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']

    print(f"开始爬取网易新闻要闻...")
    if get_all:
        print(f"设置：获取全部新闻, 获取详细内容={'是' if get_detail else '否'}")
    else:
        print(f"设置：最大新闻数量={max_news}, 获取详细内容={'是' if get_detail else '否'}")

    crawler = NeteaseNewsCrawler(max_news=max_news, get_detail=get_detail, get_all=get_all)
    news_items = crawler.crawl()

    if news_items:
        print(f"\n成功爬取 {len(news_items)} 条新闻")
        print("数据已保存到 netease_news.json 和 netease_news.csv")

        # 显示使用说明
        print("\n使用说明：")
        print("python netease_news_crawler.py [新闻数量|all] [是否获取详细内容]")
        print("例如：")
        print("  python netease_news_crawler.py 30 true    # 爬取30条新闻，包含详细内容")
        print("  python netease_news_crawler.py 50 false   # 爬取50条新闻，不包含详细内容")
        print("  python netease_news_crawler.py all true   # 爬取全部新闻，包含详细内容")
        print("  python netease_news_crawler.py all false  # 爬取全部新闻，不包含详细内容")
    else:
        print("爬取失败，请检查网络连接或网站结构是否发生变化")

if __name__ == "__main__":
    main()
