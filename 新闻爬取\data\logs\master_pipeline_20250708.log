2025-07-08 08:28:31 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-08 08:28:31 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-08 08:28:31 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=30, detail=True, all=False
2025-07-08 08:28:31 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-08 08:28:31 - MasterPipeline - INFO -    参数: max_news=30, detail=True, all=False
2025-07-08 08:28:31 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-08 08:28:32 - MasterPipeline - INFO - ⏱️ 总执行时间: 1.47 秒
2025-07-08 08:28:45 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-08 08:28:45 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-08 08:28:45 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=None, detail=True, all=True
2025-07-08 08:28:45 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-08 08:28:45 - MasterPipeline - INFO -    参数: max_news=30, detail=True, all=True
2025-07-08 08:28:45 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-08 08:56:01 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-08 08:56:01 - MasterPipeline - WARNING - ⚠️ 发现 334 个质量问题
2025-07-08 08:56:01 - MasterPipeline - WARNING -    - 新闻651缺少media字段
2025-07-08 08:56:01 - MasterPipeline - WARNING -    - 新闻652缺少media字段
2025-07-08 08:56:01 - MasterPipeline - WARNING -    - 新闻653缺少media字段
2025-07-08 08:56:01 - MasterPipeline - WARNING -    - 新闻654缺少media字段
2025-07-08 08:56:01 - MasterPipeline - WARNING -    - 新闻655缺少media字段
2025-07-08 08:56:01 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 1158条新闻, 文件大小2884886字节
2025-07-08 08:56:01 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 1158 条新闻，保存到 unified_news_20250708_085601.csv
2025-07-08 08:56:01 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-08 08:56:01 - MasterPipeline - INFO -    处理文件: unified_news_20250708_085601.csv
2025-07-08 08:56:01 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-08 08:56:14 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-08 08:56:14 - MasterPipeline - INFO - ✅ 处理结果验证通过: 成功16条, 失败0条
2025-07-08 08:56:14 - MasterPipeline - INFO - ✅ 阶段2完成: 处理 16 条新闻向量
2025-07-08 08:56:14 - MasterPipeline - INFO - 💾 阶段3: 开始数据库存储验证
2025-07-08 08:56:14 - MasterPipeline - INFO -    数据库路径: C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\news_vectors
2025-07-08 08:56:14 - MasterPipeline - INFO -    存储文档数: 1087
2025-07-08 08:56:14 - MasterPipeline - INFO - ✅ 存储结果验证通过: 1087条文档
2025-07-08 08:56:14 - MasterPipeline - INFO - 🔧 执行数据库优化...
2025-07-08 08:56:14 - MasterPipeline - ERROR - ❌ 阶段3失败: 'VectorDatabase' object has no attribute 'optimize_database'
2025-07-08 08:56:14 - MasterPipeline - ERROR - ❌ 流水线执行失败: 'VectorDatabase' object has no attribute 'optimize_database'
2025-07-08 08:56:14 - MasterPipeline - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 225, in run_full_pipeline
    stored_count = self._stage_3_storage_verification()
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 421, in _stage_3_storage_verification
    self.vector_db.optimize_database()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'VectorDatabase' object has no attribute 'optimize_database'

2025-07-08 08:56:14 - MasterPipeline - INFO - ⏱️ 总执行时间: 1649.30 秒
2025-07-08 09:08:58 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-08 09:08:58 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-08 09:08:58 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=None, detail=True, all=True
2025-07-08 09:08:58 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-08 09:08:58 - MasterPipeline - INFO -    参数: max_news=30, detail=True, all=True
2025-07-08 09:08:58 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-08 09:14:21 - MasterPipeline - INFO - ⏱️ 总执行时间: 322.94 秒
