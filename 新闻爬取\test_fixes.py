#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的问题
1. 网易爬虫重复获取详情问题
2. VectorDatabase缺少optimize_database方法
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from netease_news_crawler import NeteaseNewsCrawler
from vector_database import VectorDatabase

def test_netease_crawler():
    """测试网易爬虫是否修复重复获取问题"""
    print("=" * 60)
    print("测试1: 网易爬虫重复获取详情问题修复")
    print("=" * 60)
    
    try:
        # 创建爬虫实例，只爬取2条新闻进行测试
        crawler = NeteaseNewsCrawler(max_news=2, get_detail=True, get_all=False)
        
        # 检查是否只有一个get_article_detail方法
        methods = [method for method in dir(crawler) if method == 'get_article_detail']
        print(f"✅ get_article_detail方法数量: {len(methods)} (应该为1)")
        
        if len(methods) == 1:
            print("✅ 网易爬虫重复方法问题已修复")
            return True
        else:
            print("❌ 网易爬虫仍有重复方法问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试网易爬虫时出错: {e}")
        return False

def test_vector_database():
    """测试VectorDatabase是否有optimize_database方法"""
    print("\n" + "=" * 60)
    print("测试2: VectorDatabase optimize_database方法")
    print("=" * 60)
    
    try:
        # 创建向量数据库实例
        db = VectorDatabase("test_db_fixes", vector_dim=768)
        
        # 检查是否有optimize_database方法
        has_optimize = hasattr(db, 'optimize_database')
        print(f"✅ optimize_database方法存在: {has_optimize}")
        
        if has_optimize:
            # 添加一个测试文档
            test_vector = np.random.rand(768).tolist()
            test_metadata = {
                'title': '测试新闻',
                'content': '这是一条测试新闻内容',
                'category': 'test',
                'media': 'test_source',
                'quality_score': 0.8
            }
            
            success = db.add_document("test_001", test_vector, test_metadata)
            print(f"✅ 添加测试文档: {'成功' if success else '失败'}")
            
            # 测试optimize_database方法
            print("🔧 测试数据库优化方法...")
            db.optimize_database()
            print("✅ optimize_database方法执行成功")
            
            # 清理测试数据库
            import shutil
            if os.path.exists("test_db_fixes"):
                shutil.rmtree("test_db_fixes")
                print("🧹 清理测试数据库")
            
            return True
        else:
            print("❌ VectorDatabase缺少optimize_database方法")
            return False
            
    except Exception as e:
        print(f"❌ 测试VectorDatabase时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复的问题...")
    
    # 测试结果
    results = []
    
    # 测试1: 网易爬虫
    result1 = test_netease_crawler()
    results.append(("网易爬虫重复获取详情问题", result1))
    
    # 测试2: VectorDatabase
    result2 = test_vector_database()
    results.append(("VectorDatabase optimize_database方法", result2))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！修复成功！")
        print("\n建议:")
        print("1. 网易爬虫现在不会重复获取文章详情了")
        print("2. VectorDatabase现在支持数据库优化功能")
        print("3. 可以正常运行master_pipeline.py了")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
